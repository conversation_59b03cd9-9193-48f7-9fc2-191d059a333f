name: Test Production Build

on:
  pull_request:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    container:
      image: europe-west2-docker.pkg.dev/flash-2024-builder-8001/flash-2024-builder/flash-2024-builder:latest

    env:
      NODE_ENV: production
      ENVIRONMENT: production

    steps:
      - name: Verify Running Inside Container
        run: |
          echo "Node.js version:"
          node --version
          echo "Current user:"
          whoami
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Node.js dependencies
        run: npm install --unsafe-perm=true --allow-root --loglevel error --no-audit --maxsockets 50 --no-fund --no-update-notifier --include=dev

      - name: Create build directory
        run: mkdir -p build/professionalacademy/assets/js/

      - name: JSX production build
        run: npm run jsx:build

      - name: Tailwind build
        run: npm run tailwind:build

      - name: Flash Production Deploy
        run: flash production-deploy
